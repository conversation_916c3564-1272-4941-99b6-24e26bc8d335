import styles from './About.module.css';
import { useTranslation } from 'react-i18next';
import { useState, useEffect, useCallback } from 'react';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';

const About = () => {
  const { t } = useTranslation();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoplay, setIsAutoplay] = useState(true);

  // Используем WebP с запасным вариантом для старых браузеров
  const sliderImages = [
    { webp: '/images/webp/slider1.webp', fallback: '/images/slider1.jpg' },
    { webp: '/images/webp/slider2.webp', fallback: '/images/slider2.jpg' },
    { webp: '/images/webp/slider3.webp', fallback: '/images/slider3.jpg' },
    { webp: '/images/webp/slider4.webp', fallback: '/images/slider4.jpg' }
  ];

  const nextSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev === sliderImages.length - 1 ? 0 : prev + 1));
  }, [sliderImages.length]);

  const prevSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev === 0 ? sliderImages.length - 1 : prev - 1));
  }, [sliderImages.length]);

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  useEffect(() => {
    let interval;
    if (isAutoplay) {
      interval = setInterval(() => {
        nextSlide();
      }, 5000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isAutoplay, currentSlide, nextSlide]);

  const handleMouseEnter = () => {
    setIsAutoplay(false);
  };

  const handleMouseLeave = () => {
    setIsAutoplay(true);
  };

  return (
    <section id="about" className={styles.about}>
      <div className={styles.container}>
        <div className={styles.aboutContent}>
          <div className={styles.aboutText}>
            <div className={styles.sectionTitle}>
              <h2>{t('about.title')}</h2>
            </div>
            <p>{t('about.paragraph1')}</p>
            <p>{t('about.paragraph2')}</p>
            <p>{t('about.paragraph3')}</p>
          </div>

          <div className={styles.aboutImage} onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
            <div className={styles.slider}>
              {sliderImages.map((image, index) => (
                <picture key={index}>
                  <source srcSet={image.webp} type="image/webp" />
                  <img
                    src={image.fallback}
                    alt={`${t('about.title')} ${index + 1}`}
                    className={index === currentSlide ? styles.active : ''}
                    loading={index === 0 ? "eager" : "lazy"}
                  />
                </picture>
              ))}

              <button className={`${styles.sliderButton} ${styles.prevButton}`} onClick={prevSlide}>
                &#8249;
              </button>

              <button className={`${styles.sliderButton} ${styles.nextButton}`} onClick={nextSlide}>
                &#8250;
              </button>

              <div className={styles.sliderDots}>
                {sliderImages.map((_, index) => (
                  <span key={index} className={`${styles.dot} ${index === currentSlide ? styles.activeDot : ''}`} onClick={() => goToSlide(index)} />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
