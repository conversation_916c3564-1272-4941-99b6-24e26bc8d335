.hero {
  height: 100vh;
  min-height: 600px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url('/images/webp/hero_bg3.webp');
  /* Запасной вариант для браузеров, не поддерживающих WebP */
  background-image: image-set(
    url('/images/webp/hero_bg3.webp') type('image/webp'),
    url('/images/hero_bg3.jpeg') type('image/jpeg')
  );
  background-size: cover;
  background-position: center;
  color: var(--color-white);
  text-align: center;
  margin-top: 0;
  padding-top: 0;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 1;
}

.content {
  max-width: 800px;
  margin: 0 auto;
}

.content h1 {
  font-size: 4rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: var(--color-white);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 2px;
}

.content p {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  color: var(--color-white);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.primaryBtn {
  display: inline-block;
  padding: 1rem 2rem;
  background-color: var(--color-primary);
  color: var(--color-white);
  border-radius: 4px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
}

.primaryBtn:hover {
  background-color: var(--color-pink);
}

.secondaryBtn {
  display: inline-block;
  padding: 1rem 2rem;
  background-color: transparent;
  color: var(--color-white);
  border: 2px solid var(--color-white);
  border-radius: 4px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
}

.secondaryBtn:hover {
  background-color: var(--color-white);
  color: var(--color-black);
}

.booksyBtn {
  display: inline-block;
  padding: 1rem 2rem;
  background-color:var(--color-pink);
  color: var(--color-gray);
  border-radius: 4px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
}

.booksyBtn:hover {
  background-color: var(--color-pink);
}

.scrollIndicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  cursor: pointer;
  z-index: 10;
}

.scrollArrow {
  width: 20px;
  height: 20px;
  border-left: 2px solid var(--color-white);
  border-bottom: 2px solid var(--color-white);
  transform: rotate(-45deg);
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) rotate(-45deg);
  }
  40% {
    transform: translateY(-10px) rotate(-45deg);
  }
  60% {
    transform: translateY(-5px) rotate(-45deg);
  }
}

@media (max-width: 768px) {
  .content h1 {
    font-size: 2.5rem;
  }

  .content p {
    font-size: 1.2rem;
  }

  .buttons {
    flex-direction: column;
    gap: 0.5rem;
  }
}

