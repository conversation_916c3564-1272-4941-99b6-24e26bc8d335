.contact {
  padding: 5rem 0;
  background-color: var(--color-white);
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.sectionTitle {
  text-align: center;
  margin-bottom: 3rem;
}

.sectionTitle h2 {
  font-size: 2.5rem;
  color: var(--color-primary);
  margin-bottom: 1rem;
  position: relative;
  display: inline-block;
  padding-bottom: 1rem;
}

.sectionTitle h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: var(--color-primary);
}

.sectionTitle p {
  font-size: 1.1rem;
  color: var(--color-gray);
  max-width: 700px;
  margin: 0 auto;
}

.contactContent {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
}

.contactInfo {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.infoItem {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
}

.infoItemLink {
  text-decoration: none;
  display: block;
  transition: transform 0.3s ease;
}

.infoItemLink:hover {
}

.infoItemLink:hover .infoIcon {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.infoItemLink:hover .contactLink,
.infoItemLink:hover .booksyButton {
  color: var(--color-primary);
}

.infoIcon {
  font-size: 2rem;
  color: var(--color-primary);
  min-width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-pink-light);
  border-radius: 50%;
}

.infoText h3 {
  font-size: 1.3rem;
  color: var(--color-primary);
  margin-bottom: 0.5rem;
}

.infoText p {
  color: var(--color-gray);
  margin-bottom: 0.5rem;
}

.contactLink {
  color: var(--color-gray);
  text-decoration: none;
  transition: color 0.3s ease;
}

.contactLink:hover {
  color: var(--color-primary);
}

.booksyButton {
  color: var(--color-gray);
  font-weight: 500;
  transition: color 0.3s ease;
}

.booksyButton:hover {
  color: var(--color-primary);
}

.contactMap {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  height: 100%;
  min-height: 400px;
}

.staticMapContainer {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 400px;
}

.staticMapContainer iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

@media (max-width: 992px) {
  .contactContent {
    grid-template-columns: 1fr;
  }

  .contactMap {
    order: -1;
  }
}
