import React from 'react';

const Logo = ({ width = 40, height = 40, color = 'var(--color-primary)' }) => {
  const spinnerStyle = {
    width: `${width}px`,
    height: `${height}px`,
    border: `4px solid rgba(0, 0, 0, 0.1)`,
    borderRadius: '50%',
    borderTop: `4px solid ${color}`,
    animation: 'spin 1s linear infinite',
    marginRight: '10px'
  };

  return (
    <>
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
      <div style={spinnerStyle}></div>
    </>
  );
};

export default Logo;
