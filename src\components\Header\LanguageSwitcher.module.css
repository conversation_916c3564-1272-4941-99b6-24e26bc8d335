.languageSwitcher {
  position: relative;
  margin-left: -0.2rem;  
  margin-top: -0.3rem;
  z-index: 1001;
}

.currentLanguage {
  background: none;
  border: 1px solid var(--color-primary);
  color: var(--color-primary);
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  cursor: pointer;
  font-family: var(--font-secondary);
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.languageSwitcherOff {
  display: none;
}
.currentLanguage:hover {
  background-color: var(--color-primary);
  color: var(--color-gray);
}

.languageDropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background-color: var(--color-white);
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 0.5rem 0;
  list-style: none;
  min-width: 80px;
  z-index: 1002;
}

.languageItem {
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-family: var(--font-secondary);
  font-size: 0.9rem;
  transition: all 0.2s ease;
  text-align: center;
}

.languageItem:hover {
  background-color: rgba(70, 133, 121, 0.1);
  color: var(--color-primary);
}

.languageItem.active {
  background-color: rgba(70, 133, 121, 0.1);
  color: var(--color-primary);
  font-weight: 600;
}

.languageSwitcherOff {
  display: none;
  
}
