.about {
  padding: 5rem 0;
  background-color: var(--color-white);
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.aboutContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3rem;
}

.aboutImage {
  width: 100%;
  max-width: 900px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  position: relative;
}

.slider {
  position: relative;
  margin: 0 auto;
  height: 635px;
  overflow: hidden;
}

.slider picture {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}

.slider picture img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.slider img.active {
  opacity: 1;
}

.slider picture:has(img.active) {
  opacity: 1;
}

.sliderButton {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);font-size: 3rem;
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
  transition: all 0.3s ease;
}

.sliderButton:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.prevButton {
  left: 10px;
}

.nextButton {
  right: 10px;
}

.sliderDots {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 2;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot:hover {
  background-color: rgba(255, 255, 255, 0.8);
}

.activeDot {
  background-color: var(--color-primary);
}

.aboutText {
  width: 100%;
  max-width: 900px;
  text-align: center;
}

.sectionTitle {
  margin-bottom: 2rem;
  text-align: center;
}

.sectionTitle h2 {
  font-size: 2.5rem;
  color: var(--color-primary);
  position: relative;
  display: inline-block;
  padding-bottom: 1rem;
}

.sectionTitle h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: var(--color-primary);
}

.aboutText p {
  margin-bottom: 1.5rem;
  color: var(--color-gray);
  line-height: 1.8;
  font-size: 1.1rem;
  text-align: center;
}



@media (max-width: 992px) {
  .aboutText, .aboutImage {
    max-width: 100%;
    padding: 0 1rem;
  }

  .slider {
    height: 400px;
  }

  .sectionTitle h2 {
    font-size: 2.2rem;
  }

  .aboutText p {
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .slider {
    height: 350px;
  }

  .sliderButton {
    width: 35px;
    height: 35px;
  }

  .dot {
    width: 10px;
    height: 10px;
  }
}

@media (max-width: 576px) {
  .slider {
    height: 300px;
  }

  .aboutText p {
    font-size: 0.95rem;
  }
}
