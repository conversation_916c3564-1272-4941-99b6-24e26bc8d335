import styles from './Team.module.css'
import { useTranslation } from 'react-i18next'
const PhotoNelia = '/images/photoNelia.jpg'

const Team = () => {
  const { t } = useTranslation();
  return (
    <section id="specialist" className={styles.team}>
      <div className={styles.container}>
        <div className={styles.specialistContent}>
          <div className={styles.specialistImage}>
            <img src={PhotoNelia} alt="<PERSON><PERSON><PERSON> - Kosmetolog" />
          </div>
          <div className={styles.specialistText}>
            <div className={styles.sectionTitle}>
              <h2>{t('team.title')}</h2>
            </div>
            <p>
              {t('team.paragraph1')}</p>
            <p>
              {t('team.paragraph2')}</p>
            <p>
              {t('team.paragraph3')}</p>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Team
