import { FaWater, FaSpa, FaHandsWash, FaEye, FaInfoCircle } from 'react-icons/fa'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import styles from './Services.module.css'

const Services = () => {
  const [activeModal, setActiveModal] = useState(null);
  const { t } = useTranslation();

  const servicesCategories = [
    {
      id: 1,
      title: t('services.categories.faceCleaning.title'),
      icon: <FaWater />,
      description: t('services.categories.faceCleaning.description'),
      services: t('services.categories.faceCleaning.services', { returnObjects: true })
    },
    {
      id: 2,
      title: t('services.categories.rejuvenation.title'),
      icon: <FaSpa />,
      description: t('services.categories.rejuvenation.description'),
      services: t('services.categories.rejuvenation.services', { returnObjects: true })
    },
    {
      id: 3,
      title: t('services.categories.massage.title'),
      icon: <FaHandsWash />,
      description: t('services.categories.massage.description'),
      services: t('services.categories.massage.services', { returnObjects: true })
    },
    {
      id: 4,
      title: t('services.categories.styling.title'),
      icon: <FaEye />,
      description: t('services.categories.styling.description'),
      services: t('services.categories.styling.services', { returnObjects: true })
    }
  ];

  const openModal = (categoryId) => {
    setActiveModal(categoryId);
  };

  const closeModal = () => {
    setActiveModal(null);
  };

  return (
    <section id="services" className={styles.services}>
      <div className={styles.container}>
        <div className={styles.sectionTitle}>
          <h2>{t('services.title')}</h2>
          <p>{t('services.subtitle')}</p>
        </div>

        <div className={styles.servicesCategories}>
          {servicesCategories.map((category) => (
            <div key={category.id} className={styles.categoryCard}>
              <div className={styles.categoryHeader}>
                <div className={styles.categoryIcon}>
                  {category.icon}
                </div>
                <h3>{category.title}</h3>
                <button
                  className={styles.infoButton}
                  onClick={() => openModal(category.id)}
                  aria-label={`${t('services.infoButton')} ${category.title}`}
                >
                  <FaInfoCircle />
                </button>
              </div>
              <ul className={styles.servicesList}>
                {category.services.map((service, index) => (
                  <li key={index} className={styles.serviceItem}>{service}</li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {activeModal && (
          <div className={styles.modalOverlay} onClick={closeModal}>
            <div className={styles.modal} onClick={(e) => e.stopPropagation()}>
              <button className={styles.closeButton} onClick={closeModal}>
                &times;
              </button>
              <h3>{servicesCategories.find(cat => cat.id === activeModal)?.title}</h3>
              <p>{servicesCategories.find(cat => cat.id === activeModal)?.description}</p>
            </div>
          </div>
        )}
      </div>
    </section>
  )
}

export default Services
