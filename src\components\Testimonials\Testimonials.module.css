.testimonials {
  padding: 5rem 0;
  background-color: var(--color-beige-light);
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.sectionTitle {
  text-align: center;
  margin-bottom: 3rem;
}

.sectionTitle h2 {
  font-size: 2.5rem;
  color: var(--color-primary);
  margin-bottom: 1rem;
  position: relative;
  display: inline-block;
  padding-bottom: 1rem;
}

.sectionTitle h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: var(--color-primary);
}

.sectionTitle p {
  font-size: 1.1rem;
  color: var(--color-gray);
  max-width: 700px;
  margin: 0 auto;
}

.testimonialSlider {
  max-width: 800px;
  margin: 0 auto;
  background-color: var(--color-white);
  border-radius: 8px;
  padding: 3rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.testimonialContent {
  text-align: center;
  margin-bottom: 2rem;
}

.quoteIcon {
  font-size: 3rem;
  color: var(--color-primary);
  opacity: 0.3;
  margin-bottom: 1.5rem;
}

.testimonialText {
  font-size: 1.2rem;
  color: var(--color-gray);
  line-height: 1.8;
  margin-bottom: 1.5rem;
  font-style: italic;
}

.testimonialRating {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
}

.starFilled {
  color: #ffc107;
}

.starEmpty {
  color: #e0e0e0;
}

.testimonialAuthor {
  display: flex;
  flex-direction: column;
  align-items: center;
}


.testimonialAuthor h4 {
  font-size: 1.2rem;
  color: var(--color-primary);
  margin: 0 0 0.5rem 0;
}

.reviewSource {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--color-gray);
}

.testimonialControls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
}

.prevButton, .nextButton {
  background-color: transparent;
  border: none;
  font-size: 2rem;
  color: var(--color-primary);
  cursor: pointer;
  transition: all 0.3s ease;
}

.prevButton:hover, .nextButton:hover {
  color: var(--color-black);
  transform: scale(1.2);
}

.testimonialDots {
  display: flex;
  gap: 0.5rem;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #e0e0e0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.activeDot {
  background-color: var(--color-primary);
  transform: scale(1.2);
}

.loading, .noReviews {
  text-align: center;
  padding: 3rem;
  background-color: var(--color-white);
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  max-width: 800px;
  margin: 0 auto;
  color: var(--color-gray);
  font-size: 1.2rem;
}

@media (max-width: 768px) {
  .testimonialSlider {
    padding: 2rem;
  }

  .testimonialText {
    font-size: 1rem;
  }
}
