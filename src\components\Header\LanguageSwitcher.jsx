import { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import styles from './LanguageSwitcher.module.css';

const LanguageSwitcher = ({ className }) => {
  const { i18n, t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [currentLang, setCurrentLang] = useState('pl'); // Состояние для текущего языка
  const dropdownRef = useRef(null);

  // Доступные языки
  const languages = [
    { code: 'pl', name: t('languageSwitcher.pl') },
    { code: 'en', name: t('languageSwitcher.en') },
    { code: 'uk', name: t('languageSwitcher.uk') },
    { code: 'ru', name: t('languageSwitcher.ru') }
  ];

  // Обновляем текущий язык при изменении i18n.language или при монтировании
  useEffect(() => {
    // Функция для получения языка из localStorage или i18n
    const getCurrentLanguage = () => {
      // Сначала пробуем получить из localStorage
      const storedLang = localStorage.getItem('i18nextLng');
      if (storedLang && ['pl', 'en', 'uk', 'ru'].includes(storedLang)) {
        return storedLang;
      }
      // Если нет в localStorage, используем i18n.language
      return i18n.language || 'pl';
    };

    const updateCurrentLanguage = () => {
      const lang = getCurrentLanguage();
      setCurrentLang(lang);
    };

    // Обновляем сразу
    updateCurrentLanguage();

    // Подписываемся на изменения языка
    i18n.on('languageChanged', updateCurrentLanguage);

    return () => {
      i18n.off('languageChanged', updateCurrentLanguage);
    };
  }, [i18n]);

  const currentLanguage = languages.find(lang => lang.code === currentLang) || languages[0];

  const changeLanguage = (languageCode) => {
    i18n.changeLanguage(languageCode);
    setIsOpen(false);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className={`${styles.languageSwitcher} ${className || ''}`} ref={dropdownRef}>
      <button
        className={styles.currentLanguage}
        onClick={() => setIsOpen(!isOpen)}
      >
        {currentLanguage.name}
      </button>

      {isOpen && (
        <ul className={styles.languageDropdown}>
          {languages.map((language) => (
            <li
              key={language.code}
              className={`${styles.languageItem} ${language.code === currentLang ? styles.active : ''}`}
              onClick={() => changeLanguage(language.code)}
            >
              {language.name}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default LanguageSwitcher;
