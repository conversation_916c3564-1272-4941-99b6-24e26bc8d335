import { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import styles from './LanguageSwitcher.module.css';

const LanguageSwitcher = ({ className }) => {
  const { i18n, t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Доступные языки
  const languages = [
    { code: 'pl', name: t('languageSwitcher.pl') },
    { code: 'en', name: t('languageSwitcher.en') },
    { code: 'uk', name: t('languageSwitcher.uk') },
    { code: 'ru', name: t('languageSwitcher.ru') }
  ];

  const currentLanguage = languages.find(lang => lang.code === i18n.language) || languages[0];

  const changeLanguage = (languageCode) => {
    i18n.changeLanguage(languageCode);
    setIsOpen(false);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className={`${styles.languageSwitcher} ${className || ''}`} ref={dropdownRef}>
      <button
        className={styles.currentLanguage}
        onClick={() => setIsOpen(!isOpen)}
      >
        {currentLanguage.name}
      </button>

      {isOpen && (
        <ul className={styles.languageDropdown}>
          {languages.map((language) => (
            <li
              key={language.code}
              className={`${styles.languageItem} ${language.code === i18n.language ? styles.active : ''}`}
              onClick={() => changeLanguage(language.code)}
            >
              {language.name}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default LanguageSwitcher;
