import { FaInstagram, FaWhatsapp } from 'react-icons/fa'
import { useTranslation } from 'react-i18next'
import styles from './Footer.module.css'

const Footer = () => {
  const currentYear = new Date().getFullYear()
  const { t } = useTranslation()

  return (
    <footer className={styles.footer}>
      <div className={styles.container}>
        <div className={styles.footerContent}>
          <div className={styles.footerLogo}>
            <h2>Nelia</h2>
            <p>{t('footer.logo.subtitle')}</p>
          </div>

          <div className={styles.footerLinks}>
            <h3>{t('footer.quickLinks.title')}</h3>
            <ul>
              <li><a href="#home">{t('footer.quickLinks.home')}</a></li>
              <li><a href="#services">{t('footer.quickLinks.services')}</a></li>
              <li><a href="#about">{t('footer.quickLinks.about')}</a></li>
              <li><a href="#gallery">{t('footer.quickLinks.gallery')}</a></li>
              <li><a href="#testimonials">{t('footer.quickLinks.testimonials')}</a></li>
              <li><a href="#contact">{t('footer.quickLinks.contact')}</a></li>
            </ul>
          </div>

          <div className={styles.footerContact}>
            <h3>{t('footer.contact.title')}</h3>
            <a href="https://maps.app.goo.gl/Nt5Eo9Nt5Eo9" target="_blank" rel="noopener noreferrer" className={styles.contactLinkWrapper}>
              <p className={styles.contactLink}>{t('footer.contact.address')}</p>
            </a>
            <a href="tel:+48798046355" className={styles.contactLinkWrapper}>
              <p className={styles.contactLink}>{t('footer.contact.phone')}</p>
            </a>
          </div>

          <div className={styles.footerSocial}>
            <div className={styles.socialIcons}>
              <a href="https://www.instagram.com/kosmetolog_nelia" target="_blank" rel="noopener noreferrer" aria-label="Instagram">
                <FaInstagram />
              </a>
              <a href="https://wa.me/48798046355" target="_blank" rel="noopener noreferrer" aria-label="WhatsApp">
                <FaWhatsapp />
              </a>
            </div>
          </div>
        </div>

        <div className={styles.footerBottom}>
          <p>
            &copy; {currentYear} {t('footer.copyright')}
          </p>
          <p>
            created by <a href="https://github.com/VladyslavMutovchy" target="_blank" rel="noopener noreferrer" className={styles.authorLink}>Vladyslav Mutovchy</a>
          </p>
        </div>
      </div>
    </footer>
  )
}

export default Footer