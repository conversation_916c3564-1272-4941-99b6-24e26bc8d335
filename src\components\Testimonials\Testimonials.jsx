import { useState, useEffect } from 'react'
import { FaQuoteLeft, FaStar } from 'react-icons/fa'
import { useTranslation } from 'react-i18next'
import styles from './Testimonials.module.css'
import googleMapsReviews from '../../services/googleMapsService'
import UserAvatar from './UserAvatar'

const Testimonials = () => {
  const [activeIndex, setActiveIndex] = useState(0)
  const [testimonials, setTestimonials] = useState([])
  const [loading, setLoading] = useState(true)
  const { t } = useTranslation()

  useEffect(() => {
    setTestimonials(googleMapsReviews)
    setLoading(false)
  }, [])

  const nextTestimonial = () => {
    if (testimonials.length === 0) return
    setActiveIndex((prevIndex) => (prevIndex + 1) % testimonials.length)
  }

  const prevTestimonial = () => {
    if (testimonials.length === 0) return
    setActiveIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length)
  }

  const renderStars = (rating) => {
    return Array(5).fill(0).map((_, index) => (
      <FaStar key={index} className={index < rating ? styles.starFilled : styles.starEmpty} />
    ))
  }

  return (
    <section id="testimonials" className={styles.testimonials}>
      <div className={styles.container}>
        <div className={styles.sectionTitle}>
          <h2>{t('testimonials.title')}</h2>
          <p>{t('testimonials.subtitle')}</p>
        </div>

        {loading ? (
          <div className={styles.loading}>{t('testimonials.loading')}</div>
        ) : testimonials.length === 0 ? (
          <div className={styles.noReviews}>{t('testimonials.noReviews')}</div>
        ) : (
          <div className={styles.testimonialSlider}>
            <div className={styles.testimonialContent}>
              <div className={styles.quoteIcon}>
                <FaQuoteLeft />
              </div>
              <p className={styles.testimonialText}>{testimonials[activeIndex].text}</p>
              <div className={styles.testimonialRating}>
                {renderStars(testimonials[activeIndex].rating)}
              </div>
              <div className={styles.testimonialAuthor}>
                <UserAvatar />
                <h4>{testimonials[activeIndex].name}</h4>

              </div>
            </div>

            <div className={styles.testimonialControls}>
              <button className={styles.prevButton} onClick={prevTestimonial}>&#8249;</button>
              <div className={styles.testimonialDots}>
                {testimonials.map((_, index) => (
                  <span
                    key={index}
                    className={`${styles.dot} ${index === activeIndex ? styles.activeDot : ''}`}
                    onClick={() => setActiveIndex(index)}
                  ></span>
                ))}
              </div>
              <button className={styles.nextButton} onClick={nextTestimonial}>&#8250;</button>
            </div>
          </div>
        )}
      </div>
    </section>
  )
}

export default Testimonials
