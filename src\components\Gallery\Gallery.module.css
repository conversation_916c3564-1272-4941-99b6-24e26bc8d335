.gallery {
  padding: 5rem 0;
  background-color: var(--color-pink-light);
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.sectionTitle {
  text-align: center;
  margin-bottom: 3rem;
}

.sectionTitle h2 {
  font-size: 2.5rem;
  color: var(--color-primary);
  margin-bottom: 1rem;
  position: relative;
  display: inline-block;
  padding-bottom: 1rem;
}

.sectionTitle h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: var(--color-primary);
}

.sectionTitle p {
  font-size: 1.1rem;
  color: var(--color-gray);
  max-width: 700px;
  margin: 0 auto;
}

.galleryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  grid-auto-rows: 320px;
  gap: 1.5rem;
}

.galleryItemLarge {
  grid-row: span 2;
  height: auto !important;
}

.galleryItem {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  height: 320px;
  cursor: pointer;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}


.galleryItem img,
.videoPreview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.galleryItem:hover img,
.galleryItem:hover .videoPreview {
  transform: none;
}

.videoContainer {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.videoPlayer {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.playButton {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background-color: var(--color-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  cursor: pointer;
  z-index: 2;
  transition: all 0.3s ease;
}

.playButton svg {
  width: 30px;
  height: 30px;
}

.galleryItem:hover .playButton {
  background-color: var(--color-primary);
}

.galleryOverlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 1rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  color: var(--color-white);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.galleryItem:hover .galleryOverlay {
  opacity: 1;
}

.galleryOverlay h3 {
  font-size: 1.2rem;
  color: var(--color-white);
  margin: 0;
}

.loaderContainer {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  z-index: 5;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  padding: 5px;
}

@media (max-width: 768px) {
  .galleryGrid {
    grid-template-columns: repeat(2, 1fr);
    grid-auto-rows: 280px;
  }

  .galleryItem {
    height: 280px;
  }

  .playButton {
    width: 50px;
    height: 50px;
  }

  .playButton svg {
    width: 25px;
    height: 25px;
  }

  .galleryOverlay h3 {
    font-size: 1.1rem;
  }
}

@media (max-width: 576px) {
  .galleryGrid {
    grid-template-columns: 1fr;
    grid-auto-rows: 250px;
  }

  .galleryItem {
    height: 250px;
  }

  .playButton {
    width: 45px;
    height: 45px;
  }

  .playButton svg {
    width: 22px;
    height: 22px;
  }

  .galleryOverlay {
    padding: 0.8rem;
  }

  .galleryOverlay h3 {
    font-size: 1rem;
  }
}
