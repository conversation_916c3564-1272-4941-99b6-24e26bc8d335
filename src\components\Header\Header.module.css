.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  padding: 0.5rem 0 ;
  transition: all 0.3s ease;
  background-color: var(--color-pink-light);
}

.scrolled {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1rem 0;
}

.logo_img {
 width: 100px;
  height: 100px;
  margin: -10px 10px  -10px 0;
  flex-shrink: 0;
  object-fit: contain; 
  display: block;
}


.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo_wrapper {
  display: flex;
  align-items: center;
}

.logo {
  display: flex;
  flex-direction: column;
  align-items: left;
  margin-right: 10px;
}

.logo h1 {
  font-family: var(--font-primary);
  font-size: 2.8rem;
  font-weight: 700;
  color: var(--color-primary);
  margin: 0;
  line-height: 1;
  letter-spacing: 1px;
}

.logo p {
  font-family: var(--font-secondary);
  font-size: 0.9rem;
  color: var(--color-gray);
  margin: 0;
}

.nav {
  display: flex;
  align-items: center;
}

.languageSwitcherContainer {
  display: none;
}

.languageSwitcherOff {
  display: none;
}

.navList {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.navItem {
  margin: 0 1rem;
}

.navItem a {
  font-family: var(--font-secondary);
  font-size: 1rem;
  font-weight: 500;
  color: var(--color-black);
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
}

.navItem a:hover {
  color: var(--color-primary);
}

.navItem a::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--color-primary);
  transition: all 0.3s ease;
}

.navItem a:hover::after {
  width: 100%;
}

.headerRight {
  display: flex;
  align-items: center;
}

.mobileMenuToggle {
  display: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--color-primary);
  margin-left: 1rem;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.overlay.active {
  opacity: 1;
  visibility: visible;
}

.navItem.active a {
  color: var(--color-primary);
  font-weight: 600;
}

.navItem.active a::after {
  width: 100%;
}

.miniFooter {
  display: none;
}

.miniFooter p {
  margin-bottom: 0.5rem;
}

.miniFooter a {
  color: var(--color-gray);
  text-decoration: none;
  transition: color 0.3s ease;
  display: block;
}

.miniFooter a:hover {
  color: var(--color-primary);
}

.footerLink {
  transition: transform 0.3s ease;
}

.footerLink:hover {
}

.languageSwitcherOff {
  display: none;
}

@media (max-width: 992px) {
  .mobileMenuToggle {
    display: block;
  }

  .languageSwitcherContainer {
    display: block;
    margin-top: 1rem;
    text-align: center;
  }

  .miniFooter {
    display: block;
    margin-top: 2rem;
    text-align: center;
    color: var(--color-gray);
    font-size: 0.85rem;
    border-top: 1px solid var(--color-light-gray);
    padding-top: 1.5rem;
    width: 100%;
  }
 
  .nav {
    position: fixed;
    top: 0;
    right: -100%;
    width: 50%;
    height: 100vh;
    background-color: var(--color-white);
    flex-direction: column;
    justify-content: space-between;
    transition: all 0.5s ease;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    padding: 2rem 1rem;
    z-index: 1000;
  }

  .nav.active {
    right: 0;
  }

  .navList {
    flex-direction: column;
    align-items: center;
  }

  .navItem {
    margin: 0.6rem 0;
  }

  .navItem a {
    font-size: 0.95rem;
  }

  .miniFooter {
    display: block;
    margin-top: 2rem;
    text-align: center;
    color: var(--color-gray);
    font-size: 0.85rem;
    border-top: 1px solid var(--color-light-gray);
    padding-top: 1.5rem;
    width: 100%;
  }
}
@media (max-width: 500px) {
 .logo_subtitle {
    display: none;
  }
  .logo_img {
    width: 60px;
    height: 60px;
  }
}