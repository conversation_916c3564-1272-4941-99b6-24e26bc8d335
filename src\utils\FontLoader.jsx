import { useEffect } from 'react';

const FontLoader = () => {
  useEffect(() => {
    // Создаем элемент link для предварительной загрузки шрифтов
    const fontPreloadLink = document.createElement('link');
    fontPreloadLink.rel = 'preload';
    fontPreloadLink.href = 'https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap';
    fontPreloadLink.as = 'style';
    fontPreloadLink.crossOrigin = 'anonymous';
    document.head.appendChild(fontPreloadLink);

    // Создаем элемент link для загрузки шрифтов
    const fontLink = document.createElement('link');
    fontLink.rel = 'stylesheet';
    fontLink.href = 'https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap';
    fontLink.media = 'print';
    fontLink.onload = () => {
      fontLink.media = 'all';
    };
    document.head.appendChild(fontLink);

    return () => {
      // Удаляем элементы при размонтировании компонента
      document.head.removeChild(fontPreloadLink);
      document.head.removeChild(fontLink);
    };
  }, []);

  return null;
};

export default FontLoader;
