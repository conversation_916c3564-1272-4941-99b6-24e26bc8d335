import { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import styles from './Hero.module.css'

const Hero = () => {
  const [showScrollIndicator, setShowScrollIndicator] = useState(true)
  const { t } = useTranslation()

  useEffect(() => {
    const handleScroll = () => {
      const heroSection = document.getElementById('home')
      if (heroSection) {
        const heroHeight = heroSection.offsetHeight
        const scrollPosition = window.scrollY

        // Скрываем стрелку, когда прокручено 50% высоты секции Hero
        if (scrollPosition > heroHeight * 0.5) {
          setShowScrollIndicator(false)
        } else {
          setShowScrollIndicator(true)
        }
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <section id="home" className={styles.hero}>
      <div className={styles.overlay}></div>
      <div className={styles.container}>
        <div className={styles.content}>
          <h1>{t('hero.title')}</h1>
          <p>{t('hero.subtitle')}</p>
          <div className={styles.buttons}>
            <a href="#services" className={styles.primaryBtn}>{t('hero.buttons.services')}</a>
            <a
              href="https://booksy.com/pl-pl/266068_kosmetolog-nelia-novakovski_salon-kosmetyczny_11597_katowice"
              target="_blank"
              rel="noopener noreferrer"
              className={styles.booksyBtn}
            >
              {t('hero.buttons.appointment')}
            </a>
            <a href="#contact" className={styles.secondaryBtn}>{t('hero.buttons.contact')}</a>
          </div>
        </div>
      </div>
      {showScrollIndicator && (
        <a href="#services" className={styles.scrollIndicator}>
          <div className={styles.scrollArrow}></div>
        </a>
      )}
    </section>
  )
}

export default Hero

