import { FaMapMarkerAlt, FaPhone, FaEnvelope, FaClock, FaCalendar, FaWhatsapp, FaInstagram } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import styles from './Contact.module.css';

const Contact = () => {
  const { t } = useTranslation();
  return (
    <section id="contact" className={styles.contact}>
      <div className={styles.container}>
        <div className={styles.sectionTitle}>
          <h2>{t('contact.title')}</h2>
          <p>{t('contact.subtitle')}</p>
        </div>

        <div className={styles.contactContent}>
          <div className={styles.contactInfo}>
            <a href="https://maps.app.goo.gl/nX2irSsF76Es4WRU7" target="_blank" rel="noopener noreferrer" className={styles.infoItemLink}>
              <div className={styles.infoItem}>
                <div className={styles.infoIcon}>
                  <FaMapMarkerAlt />
                </div>
                <div className={styles.infoText}>
                  <h3>{t('contact.address.title')}</h3>
                  <p className={styles.contactLink}>{t('contact.address.value')}</p>
                </div>
              </div>
            </a>

            <a href="tel:+48798046355" className={styles.infoItemLink}>
              <div className={styles.infoItem}>
                <div className={styles.infoIcon}>
                  <FaPhone />
                </div>
                <div className={styles.infoText}>
                  <h3>{t('contact.phone.title')}</h3>
                  <p className={styles.contactLink}>{t('contact.phone.value')}</p>
                </div>
              </div>
            </a>

            <a href="https://wa.me/48798046355" target="_blank" rel="noopener noreferrer" className={styles.infoItemLink}>
              <div className={styles.infoItem}>
                <div className={styles.infoIcon}>
                  <FaWhatsapp />
                </div>
                <div className={styles.infoText}>
                  <h3>{t('contact.whatsapp.title')}</h3>
                  <p className={styles.contactLink}>{t('contact.whatsapp.value')}</p>
                </div>
              </div>
            </a>



            <a href="https://www.instagram.com/kosmetolog_nelia" target="_blank" rel="noopener noreferrer" className={styles.infoItemLink}>
              <div className={styles.infoItem}>
                <div className={styles.infoIcon}>
                  <FaInstagram />
                </div>
                <div className={styles.infoText}>
                  <h3>{t('contact.instagram.title')}</h3>
                  <p className={styles.contactLink}>{t('contact.instagram.value')}</p>
                </div>
              </div>
            </a>

            <a
              href="https://booksy.com/pl-pl/266068_kosmetolog-nelia-novakovski_salon-kosmetyczny_11597_katowice"
              target="_blank"
              rel="noopener noreferrer"
              className={styles.infoItemLink}
            >
              <div className={styles.infoItem}>
                <div className={styles.infoIcon}>
                  <FaCalendar />
                </div>
                <div className={styles.infoText}>
                  <h3>{t('contact.booking.title')}</h3>
                  <p className={styles.booksyButton}>{t('contact.booking.value')}</p>
                </div>
              </div>
            </a>
          </div>

          <div className={styles.contactMap}>
            <div className={styles.staticMapContainer}>
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2550.9569350667243!2d19.023954476913055!3d50.25538860138906!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x4716cfe2cf73b1c7%3A0x9f3fc2f28233d67a!2sKosmetolog%20Nelia%20Novakovski!5e0!3m2!1spl!2spl!4v1747319889243!5m2!1spl!2spl"
                width="100%"
                height="100%"
                style={{border: 0}}
                allowFullScreen=""
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                title={t('contact.map.alt')}
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;


