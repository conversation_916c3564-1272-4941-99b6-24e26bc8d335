import React from 'react';

const LogoPreloader = ({ width = 60, height = 60, color = 'var(--color-primary)' }) => {
  const spinnerStyle = {
    width: `${width}px`,
    height: `${height}px`,
    border: `5px solid rgba(0, 0, 0, 0.1)`,
    borderRadius: '50%',
    borderTop: `5px solid ${color}`,
    animation: 'spin 1s linear infinite',
    margin: '0 auto'
  };

  return (
    <>
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
      <div style={spinnerStyle}></div>
    </>
  );
};

export default LogoPreloader;
