.team {
  padding: 5rem 0;
  background-color: var(--color-white);
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.sectionTitle {
  margin-bottom: 2rem;
}

.sectionTitle h2 {
  font-size: 2.5rem;
  color: var(--color-primary);
  position: relative;
  display: inline-block;
  padding-bottom: 1rem;
}

.sectionTitle h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 80px;
  height: 3px;
  background-color: var(--color-primary);
}

.specialistContent {
  display: flex;
  align-items: center;
  gap: 3rem;
}

.specialistImage {
  flex: 1;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.specialistImage img {
  width: 100%;
  
  height: auto;
  display: block;
  transition: transform 0.5s ease;
}

.specialistImage:hover img {
  transform: scale(1.05);
}

.specialistText {
  flex: 1;
}

.specialistText p {
  margin-bottom: 1.5rem;
  color: var(--color-gray);
  line-height: 1.8;
}

@media (max-width: 992px) {
  .specialistContent {
    flex-direction: column;
  }

  .specialistImage, .specialistText {
    flex: none;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .sectionTitle h2 {
    font-size: 2rem;
  }

  .specialistText p {
    font-size: 1rem;
  }
}
