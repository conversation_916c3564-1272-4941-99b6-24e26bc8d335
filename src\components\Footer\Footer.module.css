.footer {
  background-color: var(--color-primary);
  color: var(--color-white);
  padding: 4rem 0 2rem;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.footerContent {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 3rem;
  margin-bottom: 3rem;
}

.footerLogo h2 {
  font-size: 2.5rem;
  color: var(--color-white);
  margin-bottom: 0.5rem;
}

.footerLogo p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
}

.footerLinks h3,
.footerContact h3,
.footerSocial h3 {
  font-size: 1.3rem;
  color: var(--color-white);
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 0.5rem;
}

.contactLink {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.5rem;
}

.contactLinkWrapper {
  text-decoration: none;
  display: block;
  transition: all 0.3s ease;
}



.contactLinkWrapper:hover .contactLink {
  color: var(--color-white);
}

.authorLink {
  color: var(--color-pink-light);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.authorLink:hover {
  color: var(--color-white);
}

.authorLink::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background-color: var(--color-white);
  transition: width 0.3s ease;
}

.authorLink:hover::after {
  width: 100%;
}

.footerLinks h3::after,
.footerContact h3::after,
.footerSocial h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 2px;
  background-color: var(--color-pink);
}

.footerLinks ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem 1rem;
}

.footerLinks li {
  margin-bottom: 0.5rem;
}

.footerLinks a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s ease;
}

.footerLinks a:hover {
  color: var(--color-white);
  padding-left: 5px;
}

.footerContact p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.8rem;
}

.socialIcons {
  display: flex;
  gap: 1rem;
}

.socialIcons a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: var(--color-white);
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.socialIcons a:hover {
  background-color: var(--color-white);
  color: var(--color-primary);
}

.footerBottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.footerBottom p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin: 0;
}

.heartIcon {
  color: var(--color-pink);
  margin: 0 0.3rem;
  animation: heartbeat 1.5s infinite;
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@media (max-width: 768px) {
  .footerContent {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footerBottom {
    flex-direction: column;
    text-align: center;
  }
}

/* Отключение hover-эффектов для сенсорных устройств */
@media (hover: none) {
  .footerLinks a:hover {
    color: rgba(255, 255, 255, 0.8) !important;
    padding-left: 0 !important;
  }

  .socialIcons a:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: var(--color-white) !important;
    transform: none !important;
  }

  .contactLinkWrapper:hover {
    transform: none !important;
  }

  .contactLinkWrapper:hover .contactLink {
    color: rgba(255, 255, 255, 0.8) !important;
  }

  .authorLink:hover {
    color: var(--color-pink-light) !important;
  }

  .authorLink:hover::after {
    width: 0 !important;
  }
}
