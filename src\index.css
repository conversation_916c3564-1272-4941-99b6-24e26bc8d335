/* Шрифты загружаются через FontLoader компонент */

:root {
  --color-primary: #c29758;
  --color-pink: #f9d6dd;
  --color-pink-light: #f9d6dd;
  --color-beige: #c9cfe4;
  --color-beige-light:  #c9cfe4;
  --color-white: #f8f5f7;
  --color-black: #383838;
  --color-gray: #474747;
  --color-light-gray: #c0c0c0;

  --font-primary: 'Cormorant Upright', "Playfair Display", serif;
  --font-secondary: 'Poppins', "Lora", sans-serif;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

body {
  font-family: var(--font-secondary);
  color: var(--color-black);
  background-color: var(--color-white);
  line-height: 1.6;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-primary);
  color: var(--color-primary);
  margin-bottom: 1rem;
  letter-spacing: 0.5px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  padding-bottom: 0.5rem;
}

h1 {
  font-size: 3rem;
  font-weight: 700;
}

h2 {
  font-size: 2.5rem;
  font-weight: 600;
}

h3 {
  font-size: 2rem;
  font-weight: 500;
}

p {
  margin-bottom: 1rem;
  color: var(--color-gray);
}

a {
  text-decoration: none;
  color: var(--color-primary);
  transition: all 0.3s ease;
}

a:hover {
  color: var(--color-black);
}

button,
.button {
  background-color: var(--color-primary);
  color: var(--color-white);
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 4px;
  font-family: var(--font-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

button:hover,
.button:hover {
  background-color:var(--color-pink);
}

img {
  max-width: 100%;
  height: auto;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.main-content {
  display: block;
  width: 100%;
}

section {
  padding: 5rem 0;
}

.section-title {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title h2 {
  position: relative;
  display: inline-block;
  padding-bottom: 1rem;
  font-size: 3rem;
  letter-spacing: 1px;
}

.section-title h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: var(--color-primary);
}

@media (max-width: 768px) {
  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  h3 {
    font-size: 1.5rem;
  }

  section {
    padding: 3rem 0;
  }
}