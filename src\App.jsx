import { useState, useEffect } from 'react'

import <PERSON><PERSON><PERSON>oa<PERSON> from './utils/FontLoader'
import Header from './components/Header/Header'
import Hero from './components/Hero/Hero'
import Services from './components/Services/Services'
import About from './components/About/About'
import Gallery from './components/Gallery/Gallery'
import Team from './components/Team/Team'
import Testimonials from './components/Testimonials/Testimonials'
import Contact from './components/Contact/Contact'
import Footer from './components/Footer/Footer'

function App() {

  const [scrolled, setScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setScrolled(true)
      } else {
        setScrolled(false)
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <>
      <FontLoader />
      <Header scrolled={scrolled} />
      <div className="main-content">
        <Hero />
        <Services />
        <About />
        <Gallery />
        <Team />
        <Testimonials />
        <Contact />
      </div>
      <Footer />
    </>
  )
}

export default App