import { useState, useEffect, useRef } from 'react';
import { FaBars, FaTimes } from 'react-icons/fa';
import classNames from 'classnames';
import { useTranslation } from 'react-i18next';
import styles from './Header.module.css';
import LanguageSwitcher from './LanguageSwitcher';

const Header = ({ scrolled }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeItem, setActiveItem] = useState('home');
  const navRef = useRef(null);
  const menuToggleRef = useRef(null);
  const { t } = useTranslation();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        isMenuOpen &&
        navRef.current &&
        !navRef.current.contains(event.target) &&
        menuToggleRef.current &&
        !menuToggleRef.current.contains(event.target)
      ) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMenuOpen]);

  useEffect(() => {
    const handleScroll = () => {
      const sections = ['home', 'services', 'about', 'gallery', 'team', 'testimonials', 'contact'];

      for (const section of sections) {
        const element = document.getElementById(section);
        if (element) {
          const rect = element.getBoundingClientRect();
          if (rect.top <= 150 && rect.bottom >= 150) {
            setActiveItem(section);
            break;
          }
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const headerClasses = classNames(styles.header, {
    [styles.scrolled]: scrolled,
  });

  return (
    <header className={headerClasses}>
      <div className={`${styles.overlay} ${isMenuOpen ? styles.active : ''}`} onClick={() => setIsMenuOpen(false)}></div>

      <div className={styles.container}>
        <div className={styles.logo_wrapper}>
          <a href="#home" onClick={() => setIsMenuOpen(false)}>
            <picture>
              <source srcSet="/images/webp/logo_small.webp" type="image/webp" />
              <img className={styles.logo_img} src="/images/logo_small.png" alt="Logo" loading="eager" />
            </picture>
          </a>
          <div className={styles.logo}>
            <h1>Nelia</h1>
            <p className={styles.logo_subtitle}>{t('header.subtitle')}</p>
          </div>
          <LanguageSwitcher className={isMenuOpen ? styles.languageSwitcherOff : ''} />
        </div>
        <div className={styles.headerRight}>
          <div className={styles.mobileMenuToggle} onClick={toggleMenu} ref={menuToggleRef}>
            {isMenuOpen ? <FaTimes /> : <FaBars />}
          </div>
        </div>

        <nav className={`${styles.nav} ${isMenuOpen ? styles.active : ''}`} ref={navRef}>
          <div>
            <ul className={styles.navList}>
              <li className={`${styles.navItem} ${activeItem === 'home' ? styles.active : ''}`}>
                <a href="#home" onClick={() => setIsMenuOpen(false)}>
                  {t('header.home')}
                </a>
              </li>
              <li className={`${styles.navItem} ${activeItem === 'services' ? styles.active : ''}`}>
                <a href="#services" onClick={() => setIsMenuOpen(false)}>
                  {t('header.services')}
                </a>
              </li>
              <li className={`${styles.navItem} ${activeItem === 'about' ? styles.active : ''}`}>
                <a href="#about" onClick={() => setIsMenuOpen(false)}>
                  {t('header.about')}
                </a>
              </li>
              <li className={`${styles.navItem} ${activeItem === 'gallery' ? styles.active : ''}`}>
                <a href="#gallery" onClick={() => setIsMenuOpen(false)}>
                  {t('header.gallery')}
                </a>
              </li>
              <li className={`${styles.navItem} ${activeItem === 'testimonials' ? styles.active : ''}`}>
                <a href="#testimonials" onClick={() => setIsMenuOpen(false)}>
                  {t('header.testimonials')}
                </a>
              </li>
              <li className={`${styles.navItem} ${activeItem === 'contact' ? styles.active : ''}`}>
                <a href="#contact" onClick={() => setIsMenuOpen(false)}>
                  {t('header.contact')}
                </a>
              </li>
            </ul>
            <div className={styles.languageSwitcherContainer}>
              <LanguageSwitcher />
            </div>
          </div>
          <div className={styles.miniFooter}>
            <p>{t('header.subtitle')}</p>
            <a href="https://maps.app.goo.gl/nX2irSsF76Es4WRU7" target="_blank" rel="noopener noreferrer" className={styles.footerLink}>
              <p>{t('header.address')}</p>
            </a>
            <a href="tel:+48798046355" className={styles.footerLink}>
              <p>+48 798 046 355</p>
            </a>
          </div>
        </nav>
      </div>
    </header>
  );
};

export default Header;
