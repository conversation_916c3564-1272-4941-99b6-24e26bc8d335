import { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import styles from './Gallery.module.css';
import LogoPreloader from '../../utils/logoPreloader';
// Используем WebP с запасным вариантом
const VideoPreview1 = {
  webp: '/images/webp/video-preview1.webp',
  fallback: '/images/video-preview1.jpg'
};
const VideoPreview2 = {
  webp: '/images/webp/video-preview2.webp',
  fallback: '/images/video-preview2.jpg'
};

const Gallery = () => {
  const [playingVideo, setPlayingVideo] = useState(null);
  const [loadingVideo, setLoadingVideo] = useState(null);
  const videoRefs = useRef({});
  const { t } = useTranslation();

  const galleryItems = [
    {
      id: 1,
      type: 'video',
      url: '/videos/video2.mp4',
      preview: VideoPreview2,
      title: t('gallery.items.facialTreatment'),
      size: 'large',
    },
    {
      id: 2,
      type: 'image',
      url: {
        webp: '/images/webp/photo5.webp',
        fallback: '/images/photo5.jpg'
      },
      title: t('gallery.items.radiolifrting'),
    },
    {
      id: 3,
      type: 'video',
      url: '/videos/video1.mp4',
      preview: VideoPreview1,
      title: t('gallery.items.facialTreatment2'),
      size: 'large',
    },
    {
      id: 4,
      type: 'image',
      url: {
        webp: '/images/webp/photo3.webp',
        fallback: '/images/photo3.jpg'
      },
      title: t('gallery.items.facialTreatments'),
    },
    {
      id: 5,
      type: 'image',
      url: {
        webp: '/images/webp/massage.webp',
        fallback: '/images/massage.jpg'
      },
      title: t('gallery.items.massage'),
    },
    {
      id: 7,
      type: 'image',
      url: {
        webp: '/images/webp/photo7.webp',
        fallback: '/images/photo7.jpg'
      },
      title: t('gallery.items.styling'),
    },
    {
      id: 8,
      type: 'image',
      url: {
        webp: '/images/webp/photo9.webp',
        fallback: '/images/photo9.jpg'
      },
      title: t('gallery.items.mesoterapia'),
    },
  ];

  const handleVideoRef = (id, element) => {
    if (element) {
      videoRefs.current[id] = element;
    }
  };

  const toggleVideo = (id, event) => {
    event.stopPropagation();

    if (playingVideo === id) {
      const video = videoRefs.current[id];
      if (video) {
        video.pause();
      }
      setPlayingVideo(null);
      return;
    }

    Object.keys(videoRefs.current).forEach((videoId) => {
      if (!videoRefs.current[videoId].paused) {
        videoRefs.current[videoId].pause();
      }
    });

    // Устанавливаем состояние загрузки
    setLoadingVideo(id);
    setPlayingVideo(id);

    setTimeout(() => {
      const video = videoRefs.current[id];
      if (video) {
        video.play().catch((error) => {
          console.error('Error playing video:', error);
          setPlayingVideo(null);
          setLoadingVideo(null);
        });
      }
    }, 100);
  };

  // Обработчик события, когда видео готово к воспроизведению
  const handleVideoCanPlay = (id) => {
    if (loadingVideo === id) {
      setLoadingVideo(null);
    }
  };

  return (
    <section id="gallery" className={styles.gallery}>
      <div className={styles.container}>
        <div className={styles.sectionTitle}>
          <h2>{t('gallery.title')}</h2>
          <p>{t('gallery.subtitle')}</p>
        </div>

        <div className={styles.galleryGrid}>
          {galleryItems.map((item) => (
            <div key={item.id} className={`${styles.galleryItem} ${item.size === 'large' ? styles.galleryItemLarge : ''}`}>
              {item.type === 'image' ? (
                <>
                  <picture>
                    <source srcSet={item.url.webp} type="image/webp" />
                    <img src={item.url.fallback} alt={`Nelia - ${item.title}`} loading="lazy" />
                  </picture>
                  <div className={styles.galleryOverlay}>
                    <h3>{item.title}</h3>
                  </div>
                </>
              ) : (
                <>
                  <div className={styles.videoContainer}>
                    {playingVideo === item.id ? (
                      <>
                        <video
                          ref={(el) => handleVideoRef(item.id, el)}
                          src={item.url}
                          className={styles.videoPlayer}
                          controls={false}
                          playsInline
                          onClick={(e) => toggleVideo(item.id, e)}
                          autoPlay
                          muted
                          loop
                          onCanPlay={() => handleVideoCanPlay(item.id)}
                        />
                        {loadingVideo === item.id && (
                          <div className={styles.loaderContainer}>
                            <LogoPreloader width={60} height={60} loop={true} />
                          </div>
                        )}
                      </>
                    ) : (
                      <>
                        <picture onClick={(e) => toggleVideo(item.id, e)}>
                          <source srcSet={item.preview.webp} type="image/webp" />
                          <img
                            src={item.preview.fallback}
                            alt={`Nelia - ${item.title}`}
                            className={styles.videoPreview}
                          />
                        </picture>
                        <div className={styles.playButton} onClick={(e) => toggleVideo(item.id, e)}>
                          <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8 5v14l11-7z" />
                          </svg>
                        </div>
                      </>
                    )}
                  </div>
                  <div className={styles.galleryOverlay}>
                    <h3>{item.title}</h3>
                  </div>
                </>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Gallery;
