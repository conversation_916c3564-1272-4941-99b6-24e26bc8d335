import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';

// Импорт файлов переводов
import translationPL from './locales/pl.json';
import translationEN from './locales/en.json';
import translationUK from './locales/uk.json';
import translationRU from './locales/ru.json';

// Ресурсы с переводами
const resources = {
  pl: {
    translation: translationPL
  },
  en: {
    translation: translationEN
  },
  uk: {
    translation: translationUK
  },
  ru: {
    translation: translationRU
  }
};

i18n
  // Загрузка переводов с сервера (для будущего расширения)
  .use(Backend)
  // Определение языка браузера
  .use(LanguageDetector)
  // Интеграция с React
  .use(initReactI18next)
  // Инициализация i18next
  .init({
    resources,
    fallbackLng: 'pl', // Язык по умолчанию
    debug: process.env.NODE_ENV === 'development',
    
    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'], // Сохранение выбранного языка в localStorage
    },
    
    interpolation: {
      escapeValue: false, // Не экранировать HTML
    }
  });

export default i18n;
