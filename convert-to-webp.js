import sharp from 'sharp';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const sourceDir = path.join(__dirname, 'public', 'images');
const targetDir = path.join(__dirname, 'public', 'images', 'webp');

if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
}

const imageFiles = fs.readdirSync(sourceDir).filter((file) => {
  const ext = path.extname(file).toLowerCase();
  return ['.jpg', '.jpeg', '.png'].includes(ext) && !file.includes('favicon');
});

async function convertImages() {
  console.log(`Found ${imageFiles.length} images to convert`);

  for (const file of imageFiles) {
    const sourcePath = path.join(sourceDir, file);
    const targetPath = path.join(targetDir, `${path.parse(file).name}.webp`);

    if (fs.existsSync(targetPath)) {
      const sourceStats = fs.statSync(sourcePath);
      const targetStats = fs.statSync(targetPath);

      if (targetStats.mtime > sourceStats.mtime) {
        console.log(`Skipping ${file} (already converted)`);
        continue;
      }
    }

    try {
      await sharp(sourcePath).webp({ quality: 80 }).toFile(targetPath);

      console.log(`Converted ${file} to WebP`);
    } catch (error) {
      console.error(`Error converting ${file}:`, error);
    }
  }

  console.log('Conversion complete!');
}

convertImages();
