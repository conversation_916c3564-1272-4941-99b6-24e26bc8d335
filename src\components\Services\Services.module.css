.services {
  padding: 5rem 0;
  background-color: var(--color-beige-light);
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.sectionTitle {
  text-align: center;
  margin-bottom: 3rem;
}

.sectionTitle h2 {
  font-size: 2.5rem;
  color: var(--color-primary);
  margin-bottom: 1rem;
  position: relative;
  display: inline-block;
  padding-bottom: 1rem;
}

.sectionTitle h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: var(--color-primary);
}

.sectionTitle p {
  font-size: 1.1rem;
  color: var(--color-gray);
  max-width: 700px;
  margin: 0 auto;
}

.servicesCategories {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-width: 900px;
  margin: 0 auto;
}

.categoryCard {
  background-color: var(--color-white);
  border-radius: 8px;
  padding: 1.5rem 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  width: 100%;
}

.categoryCard:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.categoryHeader {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  border-bottom: 2px solid var(--color-beige-light);
  padding-bottom: 0.75rem;
  position: relative;
}

.categoryIcon {
  font-size: 2rem;
  color: var(--color-primary);
  margin-right: 1rem;
}

.categoryCard h3 {
  font-size: 1.5rem;
  color: var(--color-primary);
  margin: 0;
}

.servicesList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 0.5rem 1.5rem;
}

.serviceItem {
  padding: 0.5rem 0;
  color: var(--color-gray);
  line-height: 1.6;
  position: relative;
  padding-left: 1rem;
}

.serviceItem::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--color-primary);
}



.infoButton {
  background: none;
  border: none;
  color: var(--color-primary);
  font-size: 1.5rem;
  cursor: pointer;
  margin-left: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 30%;
  transition: all 0.3s ease;
}

.infoButton:hover {
  background-color: var(--color-white);
  transform: scale(1.2);
}

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal {
  background-color: var(--color-white);
  border-radius: 8px;
  padding: 2rem;
  max-width: 500px;
  width: 100%;
  position: relative;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.modal h3 {
  color: var(--color-primary);
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.modal p {
  color: var(--color-gray);
  line-height: 1.6;
  margin-bottom: 0;
}

.closeButton {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--color-gray);
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.closeButton:hover {
  color: var(--color-primary);
  background-color: var(--color-white);
}

@media (max-width: 768px) {
  .servicesList {
    grid-template-columns: 1fr;
  }

  .modal {
    padding: 1.5rem;
    max-width: 90%;
  }
}
